const { MongoClient } = require('mongodb');

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function simpleTest() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    
    // Check LabourTime collection
    const labourTimeCount = await db.collection('LabourTime').countDocuments();
    console.log(`LabourTime entries: ${labourTimeCount}`);
    
    if (labourTimeCount > 0) {
      const sample = await db.collection('LabourTime').findOne();
      console.log('Sample LabourTime entry:');
      console.log(JSON.stringify(sample, null, 2));
    }
    
    // Check ServiceCodeAndActionType collection
    const serviceCount = await db.collection('ServiceCodeAndActionType').countDocuments();
    console.log(`\nServiceCodeAndActionType entries: ${serviceCount}`);
    
    if (serviceCount > 0) {
      const sample = await db.collection('ServiceCodeAndActionType').findOne();
      console.log('Sample ServiceCodeAndActionType entry:');
      console.log(JSON.stringify(sample, null, 2));
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

simpleTest();
