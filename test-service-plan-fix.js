import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function testServicePlanFix() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    
    // Get a sample computer
    const computer = await db.collection('CustomerComputers').findOne({});
    if (!computer) {
      console.log('No computers found');
      return;
    }
    
    console.log(`Testing with computer: ${computer.name} (${computer.model})`);
    
    // Extract computer category
    const computerCategory = computer.model?.match(/^([A-Z]+\d+)/)?.[1] || '';
    console.log(`Computer Category: ${computerCategory}`);
    
    // Get LabourTime data for this computer category
    const labourTimeData = await db.collection('LabourTime')
      .find({ ComputerCategory: computerCategory })
      .toArray();
    
    console.log(`Found ${labourTimeData.length} LabourTime records for category ${computerCategory}`);
    
    if (labourTimeData.length > 0) {
      console.log('\nSample LabourTime records:');
      labourTimeData.slice(0, 3).forEach(labour => {
        console.log(`- Service Code: ${labour['Service Code']}, Phase: ${labour.ServicePhase}, VST Hours: ${labour['VST Hours']}`);
      });
    }
    
    // Get ServiceCodeAndActionType data
    const productDesignation = computer.ProductDesignation || computer.productDesignation || 'TAD1640-42GE-B';
    const serviceCodeActionTypes = await db.collection('ServiceCodeAndActionType')
      .find({ ProductValidityGroup: productDesignation })
      .sort({ InternalNoOfHours: 1 })
      .toArray();
    
    console.log(`\nFound ${serviceCodeActionTypes.length} ServiceCodeAndActionType records for ${productDesignation}`);
    
    if (serviceCodeActionTypes.length > 0) {
      console.log('\nSample ServiceCodeAndActionType records:');
      serviceCodeActionTypes.slice(0, 3).forEach(service => {
        console.log(`- Service Code: ${service.ServiceCode}, Hours: ${service.InternalNoOfHours}, Action: ${service.ActionType}`);
        
        // Test the mapping logic
        const frequency = service.InternalNoOfHours || 1000;
        let servicePhase = '';
        
        if (frequency <= 500) {
          servicePhase = 'A';
        } else if (frequency <= 1000) {
          servicePhase = 'B';
        } else if (frequency <= 2000) {
          servicePhase = 'C';
        } else if (frequency <= 3000) {
          servicePhase = 'D';
        } else if (frequency <= 5000) {
          servicePhase = 'E';
        } else {
          servicePhase = 'S';
        }
        
        console.log(`  -> Mapped to ServicePhase: ${servicePhase}`);
        
        // Check if we can find matching LabourTime records
        const matchingLabour = labourTimeData.filter(labour => 
          labour.ServicePhase === servicePhase && labour.ComputerCategory === computerCategory
        );
        
        console.log(`  -> Found ${matchingLabour.length} matching LabourTime records`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await client.close();
  }
}

testServicePlanFix();
