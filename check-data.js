import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function checkData() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    
    // Check computers
    const computers = await db.collection('CustomerComputers').find({}).limit(3).toArray();
    console.log('\n=== COMPUTERS ===');
    computers.forEach(c => {
      console.log(`- ${c._id}: ${c.name || 'No name'} (Model: ${c.model || 'No model'})`);
    });
    
    // Check workload data
    const workloadCount = await db.collection('Workload').countDocuments();
    console.log(`\n=== WORKLOAD ===`);
    console.log(`Total workload entries: ${workloadCount}`);
    
    if (workloadCount > 0) {
      const sampleWorkload = await db.collection('Workload').find({}).limit(3).toArray();
      sampleWorkload.forEach(w => {
        console.log(`- Computer ${w.computerId}: ${w.year}/${w.month} - ${w.hours || 0} hours`);
      });
    }
    
    // Check ServiceCodeAndActionType
    const serviceCodeCount = await db.collection('ServiceCodeAndActionType').countDocuments();
    console.log(`\n=== SERVICE CODE AND ACTION TYPE ===`);
    console.log(`Total service code entries: ${serviceCodeCount}`);
    
    if (serviceCodeCount > 0) {
      const sampleServices = await db.collection('ServiceCodeAndActionType').find({}).limit(5).toArray();
      sampleServices.forEach(s => {
        console.log(`- ${s.ServiceCode} (${s.ActionType}) - Product: ${s.ProductValidityGroup} - Hours: ${s.InternalNoOfHours}`);
      });
    }
    
    // Check LabourTime
    const labourTimeCount = await db.collection('LabourTime').countDocuments();
    console.log(`\n=== LABOUR TIME ===`);
    console.log(`Total labour time entries: ${labourTimeCount}`);
    
    if (labourTimeCount > 0) {
      const sampleLabour = await db.collection('LabourTime').find({}).limit(5).toArray();
      sampleLabour.forEach(l => {
        console.log(`- ${l['Service Code']}: ${l['Service Description']} - ${l['VST Hours']} hours (Category: ${l.ComputerCategory})`);
      });
    }
    
    // Check specific product designation
    const productDesignation = 'TAD1640-42GE-B';
    const servicesForProduct = await db.collection('ServiceCodeAndActionType')
      .find({ ProductValidityGroup: productDesignation })
      .toArray();
    console.log(`\n=== SERVICES FOR ${productDesignation} ===`);
    console.log(`Found ${servicesForProduct.length} services for product designation`);
    
    if (servicesForProduct.length > 0) {
      servicesForProduct.slice(0, 3).forEach(s => {
        console.log(`- ${s.ServiceCode} (${s.ActionType}) - Hours: ${s.InternalNoOfHours}`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await client.close();
  }
}

checkData();
