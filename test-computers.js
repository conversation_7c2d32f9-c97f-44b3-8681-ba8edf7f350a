import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function testComputers() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    
    // Get available computers
    const computers = await db.collection('CustomerComputers').find({}).limit(5).toArray();
    console.log('\nAvailable computers:');
    computers.forEach(c => {
      console.log(`- ${c._id}: ${c.name || 'No name'} (Model: ${c.model || 'No model'})`);
    });
    
    if (computers.length > 0) {
      const testComputer = computers[0];
      console.log(`\nTesting with computer: ${testComputer._id}`);
      
      // Test the service plan generator
      const response = await fetch(`http://localhost:5173/api/service-plan-generator/${testComputer._id}?productDesignation=TAD1640-42GE-B`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('\nService plan generation result:');
        console.log(`- Success: ${result.success}`);
        console.log(`- Total services: ${result.servicePlan?.scheduledServices?.length || 0}`);
        
        // Check if any services have LabourTime data
        const servicesWithLabour = result.servicePlan?.scheduledServices?.filter(s => s.labourTime && s.labourTime.length > 0) || [];
        console.log(`- Services with LabourTime: ${servicesWithLabour.length}`);
        
        if (servicesWithLabour.length > 0) {
          console.log('\nFirst service with LabourTime:');
          const firstService = servicesWithLabour[0];
          console.log(`- Service Code: ${firstService.serviceCode}`);
          console.log(`- Service Phase: ${firstService.servicePhase}`);
          console.log(`- LabourTime entries: ${firstService.labourTime.length}`);
          firstService.labourTime.forEach((lt, i) => {
            console.log(`  ${i+1}. ${lt.serviceDescription} - ${lt.vstHours} hours (VST: ${lt.vstCode})`);
          });
        }
      } else {
        console.log('Failed to generate service plan:', response.status);
      }
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await client.close();
  }
}

testComputers();
